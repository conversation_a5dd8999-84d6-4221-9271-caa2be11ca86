/**
 * REMAN ERP Design System - Design Tokens
 * 
 * This file contains all design tokens (CSS custom properties) that define
 * the visual foundation of the REMAN ERP system. These tokens ensure
 * consistency across all components and pages.
 * 
 * Usage: Import this file first before any other design system files
 * @import url('./design-tokens.css');
 */

:root {
    /* ========================================
       COLOR PALETTE
       ======================================== */
    
    /* Primary Colors - Main brand colors */
    --color-primary-teal: #2EC0CB;
    --color-primary-dark-teal: #23A3AD;
    --color-primary-blue: #0F5FDC;
    --color-primary-white: #FFFFFF;

    /* Secondary Colors - Supporting colors */
    --color-secondary-dark-cyan: #17707F;
    --color-secondary-light-cyan: #36D6D9;
    --color-secondary-very-light-blue: #A4F4FF;
    --color-secondary-bright-blue: #3C91FF;
    --color-secondary-light-blue: #8CC8FA;
    --color-secondary-very-light-gray-blue: #DCE6F0;

    /* Tertiary Colors - Neutral colors */
    --color-tertiary-dark-navy: #000032;
    --color-tertiary-light-gray: #ECF3F8;
    --color-tertiary-black: #000000;

    /* Semantic Colors - Status and feedback colors */
    --color-success: #22C55E;
    --color-success-light: #10B981;
    --color-warning: #FB923C;
    --color-warning-light: #F59E0B;
    --color-error: #EF4444;
    --color-error-light: #F87171;
    --color-info: #3B82F6;
    --color-info-light: #60A5FA;

    /* Gradient Colors - For enhanced visual elements */
    --gradient-primary: linear-gradient(135deg, var(--color-primary-teal), var(--color-secondary-light-cyan));
    --gradient-secondary: linear-gradient(135deg, var(--color-tertiary-light-gray), var(--color-secondary-very-light-gray-blue));
    --gradient-success: linear-gradient(135deg, var(--color-success), var(--color-success-light));
    --gradient-warning: linear-gradient(135deg, var(--color-warning), var(--color-warning-light));
    --gradient-error: linear-gradient(135deg, var(--color-error), var(--color-error-light));

    /* ========================================
       TYPOGRAPHY
       ======================================== */
    
    /* Font Families */
    --font-family-primary: 'HCLTech Roobert', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    --font-family-monospace: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

    /* Font Weights */
    --font-weight-light: 300;
    --font-weight-regular: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;

    /* Font Sizes - Responsive scale */
    --font-size-xs: 0.75rem;    /* 12px */
    --font-size-sm: 0.8rem;     /* 13px */
    --font-size-base: 0.9rem;   /* 14px */
    --font-size-md: 1rem;       /* 16px */
    --font-size-lg: 1.125rem;   /* 18px */
    --font-size-xl: 1.25rem;    /* 20px */
    --font-size-2xl: 1.5rem;    /* 24px */
    --font-size-3xl: 1.75rem;   /* 28px */
    --font-size-4xl: 2rem;      /* 32px */
    --font-size-5xl: 2.25rem;   /* 36px */

    /* Line Heights */
    --line-height-tight: 1.2;
    --line-height-normal: 1.4;
    --line-height-relaxed: 1.5;
    --line-height-loose: 1.6;

    /* Letter Spacing */
    --letter-spacing-tight: -0.02em;
    --letter-spacing-normal: 0;
    --letter-spacing-wide: 0.025em;
    --letter-spacing-wider: 0.05em;

    /* ========================================
       SPACING SYSTEM
       ======================================== */
    
    /* Base spacing unit: 0.25rem (4px) */
    --spacing-0: 0;
    --spacing-px: 1px;
    --spacing-0-5: 0.125rem;    /* 2px */
    --spacing-1: 0.25rem;       /* 4px */
    --spacing-1-5: 0.375rem;    /* 6px */
    --spacing-2: 0.5rem;        /* 8px */
    --spacing-2-5: 0.625rem;    /* 10px */
    --spacing-3: 0.75rem;       /* 12px */
    --spacing-3-5: 0.875rem;    /* 14px */
    --spacing-4: 1rem;          /* 16px */
    --spacing-5: 1.25rem;       /* 20px */
    --spacing-6: 1.5rem;        /* 24px */
    --spacing-7: 1.75rem;       /* 28px */
    --spacing-8: 2rem;          /* 32px */
    --spacing-9: 2.25rem;       /* 36px */
    --spacing-10: 2.5rem;       /* 40px */
    --spacing-12: 3rem;         /* 48px */
    --spacing-16: 4rem;         /* 64px */
    --spacing-20: 5rem;         /* 80px */
    --spacing-24: 6rem;         /* 96px */

    /* Semantic spacing aliases */
    --spacing-xs: var(--spacing-1);     /* 4px */
    --spacing-sm: var(--spacing-2);     /* 8px */
    --spacing-md: var(--spacing-4);     /* 16px */
    --spacing-lg: var(--spacing-6);     /* 24px */
    --spacing-xl: var(--spacing-8);     /* 32px */
    --spacing-xxl: var(--spacing-12);   /* 48px */

    /* ========================================
       BORDER RADIUS
       ======================================== */
    
    --border-radius-none: 0;
    --border-radius-xs: 2px;
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;
    --border-radius-2xl: 20px;
    --border-radius-3xl: 24px;
    --border-radius-full: 50%;

    /* ========================================
       SHADOWS
       ======================================== */
    
    --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
    --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.15);
    --shadow-inner: inset 0 2px 4px rgba(0, 0, 0, 0.06);

    /* Colored shadows for interactive elements */
    --shadow-primary: 0 4px 12px rgba(46, 192, 203, 0.15);
    --shadow-primary-lg: 0 8px 25px rgba(46, 192, 203, 0.25);
    --shadow-success: 0 4px 12px rgba(34, 197, 94, 0.15);
    --shadow-warning: 0 4px 12px rgba(251, 146, 60, 0.15);
    --shadow-error: 0 4px 12px rgba(239, 68, 68, 0.15);

    /* ========================================
       TRANSITIONS & ANIMATIONS
       ======================================== */
    
    /* Transition Durations */
    --duration-fast: 0.15s;
    --duration-normal: 0.3s;
    --duration-slow: 0.5s;

    /* Transition Timing Functions */
    --ease-linear: linear;
    --ease-in: ease-in;
    --ease-out: ease-out;
    --ease-in-out: ease-in-out;
    --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --ease-smooth: cubic-bezier(0.4, 0, 0.2, 1);

    /* Common Transitions */
    --transition-fast: var(--duration-fast) var(--ease-in-out);
    --transition-normal: var(--duration-normal) var(--ease-in-out);
    --transition-slow: var(--duration-slow) var(--ease-in-out);
    --transition-smooth: var(--duration-normal) var(--ease-smooth);
    --transition-bounce: var(--duration-normal) var(--ease-bounce);

    /* ========================================
       Z-INDEX SCALE
       ======================================== */
    
    --z-index-dropdown: 1000;
    --z-index-sticky: 1001;
    --z-index-fixed: 1002;
    --z-index-modal-backdrop: 1003;
    --z-index-modal: 1004;
    --z-index-popover: 1005;
    --z-index-tooltip: 1006;
    --z-index-toast: 1007;

    /* ========================================
       BREAKPOINTS
       ======================================== */
    
    --breakpoint-xs: 480px;
    --breakpoint-sm: 640px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 1024px;
    --breakpoint-xl: 1280px;
    --breakpoint-2xl: 1536px;

    /* ========================================
       COMPONENT SPECIFIC TOKENS
       ======================================== */
    
    /* Button Heights */
    --button-height-sm: 32px;
    --button-height-md: 40px;
    --button-height-lg: 48px;

    /* Input Heights */
    --input-height-sm: 36px;
    --input-height-md: 44px;
    --input-height-lg: 52px;

    /* Container Max Widths */
    --container-xs: 480px;
    --container-sm: 640px;
    --container-md: 768px;
    --container-lg: 1024px;
    --container-xl: 1280px;
    --container-2xl: 1536px;
}

/* ========================================
   DARK MODE SUPPORT (Optional)
   ======================================== */

@media (prefers-color-scheme: dark) {
    :root {
        /* Override specific colors for dark mode if needed */
        --color-tertiary-light-gray: #1a1a1a;
        --color-primary-white: #ffffff;
        --color-tertiary-black: #ffffff;
        --color-tertiary-dark-navy: #ffffff;
    }
}

/* ========================================
   REDUCED MOTION SUPPORT
   ======================================== */

@media (prefers-reduced-motion: reduce) {
    :root {
        --duration-fast: 0.01ms;
        --duration-normal: 0.01ms;
        --duration-slow: 0.01ms;
        --transition-fast: none;
        --transition-normal: none;
        --transition-slow: none;
        --transition-smooth: none;
        --transition-bounce: none;
    }
}
