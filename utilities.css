/**
 * REMAN ERP Design System - Utilities
 * 
 * This file contains utility classes for common styling needs.
 * Import after design-tokens.css.
 * 
 * Usage: @import url('./utilities.css');
 */

/* ========================================
   BACKGROUND UTILITIES
   ======================================== */

.bg-primary { background-color: var(--color-primary-teal); }
.bg-primary-dark { background-color: var(--color-primary-dark-teal); }
.bg-secondary { background-color: var(--color-secondary-dark-cyan); }
.bg-tertiary { background-color: var(--color-tertiary-dark-navy); }
.bg-light { background-color: var(--color-tertiary-light-gray); }
.bg-white { background-color: var(--color-primary-white); }
.bg-success { background-color: var(--color-success); }
.bg-warning { background-color: var(--color-warning); }
.bg-error { background-color: var(--color-error); }
.bg-info { background-color: var(--color-info); }

/* Gradient Backgrounds */
.bg-gradient-primary { background: var(--gradient-primary); }
.bg-gradient-secondary { background: var(--gradient-secondary); }
.bg-gradient-success { background: var(--gradient-success); }
.bg-gradient-warning { background: var(--gradient-warning); }
.bg-gradient-error { background: var(--gradient-error); }

/* ========================================
   BORDER UTILITIES
   ======================================== */

.border { border: 1px solid var(--color-secondary-very-light-gray-blue); }
.border-primary { border: 1px solid var(--color-primary-teal); }
.border-secondary { border: 1px solid var(--color-secondary-dark-cyan); }
.border-success { border: 1px solid var(--color-success); }
.border-warning { border: 1px solid var(--color-warning); }
.border-error { border: 1px solid var(--color-error); }
.border-none { border: none; }

/* Border Widths */
.border-0 { border-width: 0; }
.border-1 { border-width: 1px; }
.border-2 { border-width: 2px; }
.border-4 { border-width: 4px; }

/* Border Radius */
.rounded-none { border-radius: var(--border-radius-none); }
.rounded-xs { border-radius: var(--border-radius-xs); }
.rounded-sm { border-radius: var(--border-radius-sm); }
.rounded-md { border-radius: var(--border-radius-md); }
.rounded-lg { border-radius: var(--border-radius-lg); }
.rounded-xl { border-radius: var(--border-radius-xl); }
.rounded-2xl { border-radius: var(--border-radius-2xl); }
.rounded-3xl { border-radius: var(--border-radius-3xl); }
.rounded-full { border-radius: var(--border-radius-full); }

/* ========================================
   SHADOW UTILITIES
   ======================================== */

.shadow-none { box-shadow: none; }
.shadow-xs { box-shadow: var(--shadow-xs); }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-2xl { box-shadow: var(--shadow-2xl); }
.shadow-inner { box-shadow: var(--shadow-inner); }

/* Colored Shadows */
.shadow-primary { box-shadow: var(--shadow-primary); }
.shadow-primary-lg { box-shadow: var(--shadow-primary-lg); }
.shadow-success { box-shadow: var(--shadow-success); }
.shadow-warning { box-shadow: var(--shadow-warning); }
.shadow-error { box-shadow: var(--shadow-error); }

/* ========================================
   OPACITY UTILITIES
   ======================================== */

.opacity-0 { opacity: 0; }
.opacity-25 { opacity: 0.25; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

/* ========================================
   TRANSFORM UTILITIES
   ======================================== */

.transform { transform: translateX(0) translateY(0) rotate(0) skewX(0) skewY(0) scaleX(1) scaleY(1); }
.transform-none { transform: none; }

/* Scale */
.scale-0 { transform: scale(0); }
.scale-50 { transform: scale(0.5); }
.scale-75 { transform: scale(0.75); }
.scale-90 { transform: scale(0.9); }
.scale-95 { transform: scale(0.95); }
.scale-100 { transform: scale(1); }
.scale-105 { transform: scale(1.05); }
.scale-110 { transform: scale(1.1); }
.scale-125 { transform: scale(1.25); }

/* Rotate */
.rotate-0 { transform: rotate(0deg); }
.rotate-45 { transform: rotate(45deg); }
.rotate-90 { transform: rotate(90deg); }
.rotate-180 { transform: rotate(180deg); }
.rotate-270 { transform: rotate(270deg); }

/* Translate */
.translate-x-0 { transform: translateX(0); }
.translate-x-1 { transform: translateX(var(--spacing-1)); }
.translate-x-2 { transform: translateX(var(--spacing-2)); }
.translate-x-4 { transform: translateX(var(--spacing-4)); }
.translate-y-0 { transform: translateY(0); }
.translate-y-1 { transform: translateY(var(--spacing-1)); }
.translate-y-2 { transform: translateY(var(--spacing-2)); }
.translate-y-4 { transform: translateY(var(--spacing-4)); }

/* ========================================
   TRANSITION UTILITIES
   ======================================== */

.transition-none { transition: none; }
.transition-all { transition: all var(--transition-normal); }
.transition-colors { transition: color var(--transition-normal), background-color var(--transition-normal), border-color var(--transition-normal); }
.transition-opacity { transition: opacity var(--transition-normal); }
.transition-shadow { transition: box-shadow var(--transition-normal); }
.transition-transform { transition: transform var(--transition-normal); }

/* Transition Durations */
.duration-fast { transition-duration: var(--duration-fast); }
.duration-normal { transition-duration: var(--duration-normal); }
.duration-slow { transition-duration: var(--duration-slow); }

/* ========================================
   CURSOR UTILITIES
   ======================================== */

.cursor-auto { cursor: auto; }
.cursor-default { cursor: default; }
.cursor-pointer { cursor: pointer; }
.cursor-wait { cursor: wait; }
.cursor-text { cursor: text; }
.cursor-move { cursor: move; }
.cursor-help { cursor: help; }
.cursor-not-allowed { cursor: not-allowed; }

/* ========================================
   USER SELECT UTILITIES
   ======================================== */

.select-none { user-select: none; }
.select-text { user-select: text; }
.select-all { user-select: all; }
.select-auto { user-select: auto; }

/* ========================================
   POINTER EVENTS UTILITIES
   ======================================== */

.pointer-events-none { pointer-events: none; }
.pointer-events-auto { pointer-events: auto; }

/* ========================================
   RESIZE UTILITIES
   ======================================== */

.resize-none { resize: none; }
.resize { resize: both; }
.resize-y { resize: vertical; }
.resize-x { resize: horizontal; }

/* ========================================
   OBJECT FIT UTILITIES
   ======================================== */

.object-contain { object-fit: contain; }
.object-cover { object-fit: cover; }
.object-fill { object-fit: fill; }
.object-none { object-fit: none; }
.object-scale-down { object-fit: scale-down; }

/* ========================================
   VISIBILITY UTILITIES
   ======================================== */

.visible { visibility: visible; }
.invisible { visibility: hidden; }

/* ========================================
   WHITESPACE UTILITIES
   ======================================== */

.whitespace-normal { white-space: normal; }
.whitespace-nowrap { white-space: nowrap; }
.whitespace-pre { white-space: pre; }
.whitespace-pre-line { white-space: pre-line; }
.whitespace-pre-wrap { white-space: pre-wrap; }

/* ========================================
   WORD BREAK UTILITIES
   ======================================== */

.break-normal { overflow-wrap: normal; word-break: normal; }
.break-words { overflow-wrap: break-word; }
.break-all { word-break: break-all; }

/* ========================================
   TRUNCATE UTILITIES
   ======================================== */

.truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
}

.line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
}

.line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
}

/* ========================================
   ASPECT RATIO UTILITIES
   ======================================== */

.aspect-square { aspect-ratio: 1 / 1; }
.aspect-video { aspect-ratio: 16 / 9; }
.aspect-auto { aspect-ratio: auto; }

/* ========================================
   SCREEN READER UTILITIES
   ======================================== */

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.not-sr-only {
    position: static;
    width: auto;
    height: auto;
    padding: 0;
    margin: 0;
    overflow: visible;
    clip: auto;
    white-space: normal;
}

/* ========================================
   FOCUS UTILITIES
   ======================================== */

.focus-outline {
    outline: 2px solid var(--color-primary-teal);
    outline-offset: 2px;
}

.focus-ring {
    box-shadow: 0 0 0 3px rgba(46, 192, 203, 0.1);
}

/* ========================================
   HOVER EFFECTS
   ======================================== */

.hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-fade:hover {
    opacity: 0.8;
}

/* ========================================
   ANIMATION UTILITIES
   ======================================== */

.animate-spin {
    animation: spin 1s linear infinite;
}

.animate-ping {
    animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce {
    animation: bounce 1s infinite;
}

@keyframes ping {
    75%, 100% {
        transform: scale(2);
        opacity: 0;
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: .5;
    }
}

@keyframes bounce {
    0%, 100% {
        transform: translateY(-25%);
        animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
    }
    50% {
        transform: translateY(0);
        animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
    }
}
