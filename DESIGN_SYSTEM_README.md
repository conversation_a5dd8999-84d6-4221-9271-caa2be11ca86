# REMAN ERP Design System

A comprehensive, scalable design system extracted from the REMAN ERP application to ensure visual consistency and maintainability across all team development projects.

## 📋 Table of Contents

- [Overview](#overview)
- [Quick Start](#quick-start)
- [File Structure](#file-structure)
- [Design Tokens](#design-tokens)
- [Typography](#typography)
- [Layout System](#layout-system)
- [Components](#components)
- [Usage Examples](#usage-examples)
- [Best Practices](#best-practices)
- [Browser Support](#browser-support)
- [Contributing](#contributing)

## 🎯 Overview

The REMAN ERP Design System provides a unified set of design tokens, components, and guidelines that ensure consistency across all web applications in the REMAN ecosystem. It includes:

- **Design Tokens**: Colors, typography, spacing, shadows, and transitions
- **Typography System**: Font definitions and text styles
- **Layout Utilities**: Flexbox, grid, and spacing utilities
- **Component Library**: Reusable UI components
- **Interactive Documentation**: Live examples and usage guidelines

## 🚀 Quick Start

### 1. Import the Design System

Add these imports to your CSS file in the following order:

```css
/* Required: Import design tokens first */
@import url('./design-tokens.css');

/* Core systems */
@import url('./typography.css');
@import url('./layout.css');

/* Components (optional, import only what you need) */
@import url('./components.css');
```

### 2. Basic HTML Structure

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your App</title>
    <!-- Import design system -->
    <link rel="stylesheet" href="path/to/design-tokens.css">
    <link rel="stylesheet" href="path/to/typography.css">
    <link rel="stylesheet" href="path/to/layout.css">
    <link rel="stylesheet" href="path/to/components.css">
</head>
<body>
    <div class="container">
        <h1 class="heading-1">Welcome to REMAN ERP</h1>
        <button class="btn btn-primary">Get Started</button>
    </div>
</body>
</html>
```

### 3. View the Documentation

Open `design-system-guide.html` in your browser to see live examples and usage guidelines.

## 📁 File Structure

```
design-system/
├── design-tokens.css          # Core design tokens (colors, spacing, etc.)
├── typography.css             # Font definitions and text styles
├── layout.css                 # Layout utilities and grid system
├── components.css             # Reusable UI components
├── design-system-guide.html   # Interactive documentation
├── DESIGN_SYSTEM_README.md    # This file
└── HCLTech Roobert_font/      # Custom font files
```

## 🎨 Design Tokens

### Color Palette

```css
/* Primary Colors */
--color-primary-teal: #2EC0CB;
--color-primary-dark-teal: #23A3AD;
--color-primary-blue: #0F5FDC;
--color-tertiary-dark-navy: #000032;

/* Semantic Colors */
--color-success: #22C55E;
--color-warning: #FB923C;
--color-error: #EF4444;
--color-info: #3B82F6;
```

### Spacing Scale

```css
--spacing-xs: 0.25rem;    /* 4px */
--spacing-sm: 0.5rem;     /* 8px */
--spacing-md: 1rem;       /* 16px */
--spacing-lg: 1.5rem;     /* 24px */
--spacing-xl: 2rem;       /* 32px */
--spacing-xxl: 3rem;      /* 48px */
```

### Typography Scale

```css
--font-size-xs: 0.75rem;    /* 12px */
--font-size-sm: 0.8rem;     /* 13px */
--font-size-base: 0.9rem;   /* 14px */
--font-size-md: 1rem;       /* 16px */
--font-size-lg: 1.125rem;   /* 18px */
--font-size-xl: 1.25rem;    /* 20px */
--font-size-2xl: 1.5rem;    /* 24px */
```

## 📝 Typography

### Heading Classes

```html
<h1 class="heading-1">Main Page Title</h1>
<h2 class="heading-2">Section Title</h2>
<h3 class="heading-3">Subsection Title</h3>
<h4 class="heading-4">Component Title</h4>
```

### Text Utilities

```html
<p class="text-body">Regular body text</p>
<p class="text-body-small">Smaller body text</p>
<p class="text-caption">Caption text</p>
<p class="text-overline">OVERLINE TEXT</p>
```

### Font Weight Classes

```html
<span class="font-light">Light text</span>
<span class="font-regular">Regular text</span>
<span class="font-medium">Medium text</span>
<span class="font-semibold">Semibold text</span>
<span class="font-bold">Bold text</span>
```

## 📐 Layout System

### Container System

```html
<div class="container container-lg">
    <!-- Content with max-width and auto margins -->
</div>
```

### Flexbox Utilities

```html
<div class="flex justify-between items-center">
    <div>Left content</div>
    <div>Right content</div>
</div>
```

### Grid System

```html
<div class="grid grid-cols-3 gap-4">
    <div>Item 1</div>
    <div>Item 2</div>
    <div>Item 3</div>
</div>
```

### Spacing Utilities

```html
<div class="p-4 m-2">Padding 16px, Margin 8px</div>
<div class="px-6 py-3">Horizontal padding 24px, Vertical padding 12px</div>
```

## 🧩 Components

### Buttons

```html
<button class="btn btn-primary">Primary Action</button>
<button class="btn btn-secondary">Secondary Action</button>
<button class="btn btn-outline">Outline Button</button>
<button class="btn btn-ghost">Ghost Button</button>

<!-- Button sizes -->
<button class="btn btn-primary btn-sm">Small</button>
<button class="btn btn-primary">Default</button>
<button class="btn btn-primary btn-lg">Large</button>
```

### Form Elements

```html
<div class="form-group">
    <label class="form-label required">Email</label>
    <input type="email" class="form-input" placeholder="Enter email">
    <div class="form-help">Helper text</div>
</div>

<div class="form-group">
    <label class="form-label">Message</label>
    <textarea class="form-textarea" placeholder="Enter message"></textarea>
</div>
```

### Cards

```html
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Card Title</h3>
        <p class="card-subtitle">Subtitle</p>
    </div>
    <div class="card-body">
        <p>Card content goes here.</p>
    </div>
    <div class="card-footer">
        <button class="btn btn-primary">Action</button>
    </div>
</div>
```

### Badges

```html
<span class="badge badge-primary">Primary</span>
<span class="badge badge-success">Success</span>
<span class="badge badge-warning">Warning</span>
<span class="badge badge-error">Error</span>
```

### Alerts

```html
<div class="alert alert-info">Information message</div>
<div class="alert alert-success">Success message</div>
<div class="alert alert-warning">Warning message</div>
<div class="alert alert-error">Error message</div>
```

### Navigation

```html
<nav class="nav">
    <a href="#" class="nav-link active">Home</a>
    <a href="#" class="nav-link">About</a>
    <a href="#" class="nav-link">Contact</a>
</nav>
```

### Tabs

```html
<div class="tabs">
    <div class="tab-list">
        <button class="tab-button active">Tab 1</button>
        <button class="tab-button">Tab 2</button>
        <button class="tab-button">Tab 3</button>
    </div>
    <div class="tab-content active">
        <p>Content for tab 1</p>
    </div>
    <div class="tab-content">
        <p>Content for tab 2</p>
    </div>
</div>
```

## 💡 Best Practices

### 1. Use Design Tokens

❌ **Don't use hardcoded values:**
```css
.my-component {
    color: #2EC0CB;
    padding: 16px;
    border-radius: 8px;
}
```

✅ **Use design tokens:**
```css
.my-component {
    color: var(--color-primary-teal);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
}
```

### 2. Follow the Spacing System

❌ **Don't use arbitrary spacing:**
```css
.my-element {
    margin: 13px 7px 21px 15px;
}
```

✅ **Use the spacing scale:**
```css
.my-element {
    margin: var(--spacing-3) var(--spacing-2) var(--spacing-5) var(--spacing-4);
}
```

### 3. Use Semantic Classes

❌ **Don't use generic classes:**
```html
<button class="blue-button">Submit</button>
```

✅ **Use semantic component classes:**
```html
<button class="btn btn-primary">Submit</button>
```

### 4. Maintain Consistency

- Always use the same component for the same purpose
- Follow the established visual hierarchy
- Use consistent spacing patterns
- Stick to the defined color palette

### 5. Responsive Design

- Test components on different screen sizes
- Use responsive utilities when needed
- Ensure touch targets are at least 44px on mobile

## 🌐 Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 🤝 Contributing

### Adding New Components

1. Follow the existing naming conventions
2. Use design tokens for all values
3. Include hover and focus states
4. Add responsive considerations
5. Update the documentation

### Modifying Existing Components

1. Ensure backward compatibility
2. Test across all supported browsers
3. Update documentation and examples
4. Consider impact on existing implementations

### Design Token Changes

1. Discuss changes with the team first
2. Update all affected components
3. Test thoroughly across the application
4. Document the changes and migration path

## 📞 Support

For questions, issues, or contributions, please contact the REMAN ERP development team or create an issue in the project repository.

---

**Version:** 1.0.0  
**Last Updated:** 2024  
**Maintained by:** REMAN ERP Development Team
