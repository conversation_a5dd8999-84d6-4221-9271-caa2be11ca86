/**
 * REMAN ERP Design System - Components
 *
 * This file contains reusable UI components. Import design-tokens.css,
 * typography.css, and layout.css first.
 *
 * Usage: @import url('./components.css');
 */

/* ========================================
   BUTTON COMPONENTS
   ======================================== */

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-4);
    border: 1px solid transparent;
    border-radius: var(--border-radius-md);
    font-family: var(--font-family-primary);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    line-height: 1;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-normal);
    user-select: none;
    white-space: nowrap;
    min-height: var(--button-height-md);
}

.btn:focus {
    outline: 2px solid var(--color-primary-teal);
    outline-offset: 2px;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* Button Variants */
.btn-primary {
    background: var(--color-primary-teal);
    color: var(--color-primary-white);
    border-color: var(--color-primary-teal);
}

.btn-primary:hover {
    background: var(--color-primary-dark-teal);
    border-color: var(--color-primary-dark-teal);
    transform: translateY(-1px);
    box-shadow: var(--shadow-primary);
}

.btn-secondary {
    background: var(--color-primary-white);
    color: var(--color-primary-teal);
    border-color: var(--color-primary-teal);
}

.btn-secondary:hover {
    background: var(--color-secondary-very-light-blue);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-outline {
    background: transparent;
    color: var(--color-tertiary-dark-navy);
    border-color: var(--color-secondary-very-light-gray-blue);
}

.btn-outline:hover {
    background: var(--color-tertiary-light-gray);
    border-color: var(--color-primary-teal);
    color: var(--color-primary-teal);
}

.btn-ghost {
    background: transparent;
    color: var(--color-secondary-dark-cyan);
    border-color: transparent;
}

.btn-ghost:hover {
    background: var(--color-tertiary-light-gray);
    color: var(--color-primary-teal);
}

/* Button Sizes */
.btn-sm {
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--font-size-sm);
    min-height: var(--button-height-sm);
}

.btn-lg {
    padding: var(--spacing-4) var(--spacing-6);
    font-size: var(--font-size-lg);
    min-height: var(--button-height-lg);
}

/* Button States */
.btn-loading {
    position: relative;
    color: transparent;
}

.btn-loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    border: 2px solid currentColor;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* ========================================
   FORM COMPONENTS
   ======================================== */

.form-group {
    margin-bottom: var(--spacing-4);
}

.form-label {
    display: block;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--color-tertiary-dark-navy);
    margin-bottom: var(--spacing-2);
}

.form-label.required::after {
    content: ' *';
    color: var(--color-error);
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: var(--spacing-3) var(--spacing-4);
    border: 1px solid var(--color-secondary-very-light-gray-blue);
    border-radius: var(--border-radius-md);
    background: var(--color-primary-white);
    font-family: var(--font-family-primary);
    font-size: var(--font-size-base);
    color: var(--color-tertiary-black);
    transition: var(--transition-normal);
    min-height: var(--input-height-md);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--color-primary-teal);
    box-shadow: 0 0 0 3px rgba(46, 192, 203, 0.1);
}

.form-input::placeholder,
.form-textarea::placeholder {
    color: var(--color-secondary-dark-cyan);
    opacity: 0.7;
}

.form-input.error,
.form-select.error,
.form-textarea.error {
    border-color: var(--color-error);
}

.form-input.error:focus,
.form-select.error:focus,
.form-textarea.error:focus {
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

.form-help {
    font-size: var(--font-size-sm);
    color: var(--color-secondary-dark-cyan);
    margin-top: var(--spacing-1);
}

.form-error {
    font-size: var(--font-size-sm);
    color: var(--color-error);
    margin-top: var(--spacing-1);
}

/* Input Sizes */
.form-input-sm,
.form-select-sm {
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--font-size-sm);
    min-height: var(--input-height-sm);
}

.form-input-lg,
.form-select-lg {
    padding: var(--spacing-4) var(--spacing-5);
    font-size: var(--font-size-lg);
    min-height: var(--input-height-lg);
}

/* ========================================
   CARD COMPONENTS
   ======================================== */

.card {
    background: var(--color-primary-white);
    border: 1px solid var(--color-secondary-very-light-gray-blue);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: var(--transition-normal);
}

.card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.card-header {
    padding: var(--spacing-4) var(--spacing-6);
    border-bottom: 1px solid var(--color-secondary-very-light-gray-blue);
    background: var(--color-tertiary-light-gray);
}

.card-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--color-tertiary-dark-navy);
    margin: 0;
}

.card-subtitle {
    font-size: var(--font-size-sm);
    color: var(--color-secondary-dark-cyan);
    margin: var(--spacing-1) 0 0 0;
}

.card-body {
    padding: var(--spacing-6);
}

.card-footer {
    padding: var(--spacing-4) var(--spacing-6);
    border-top: 1px solid var(--color-secondary-very-light-gray-blue);
    background: var(--color-tertiary-light-gray);
}

/* Card Variants */
.card-elevated {
    box-shadow: var(--shadow-lg);
}

.card-bordered {
    border: 2px solid var(--color-primary-teal);
}

.card-gradient {
    background: var(--gradient-primary);
    color: var(--color-primary-white);
}

.card-gradient .card-title {
    color: var(--color-primary-white);
}

/* ========================================
   BADGE COMPONENTS
   ======================================== */

.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-1) var(--spacing-2);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    border-radius: var(--border-radius-full);
    text-transform: uppercase;
    letter-spacing: var(--letter-spacing-wide);
}

.badge-primary {
    background: var(--color-primary-teal);
    color: var(--color-primary-white);
}

.badge-secondary {
    background: var(--color-secondary-very-light-gray-blue);
    color: var(--color-secondary-dark-cyan);
}

.badge-success {
    background: var(--color-success);
    color: var(--color-primary-white);
}

.badge-warning {
    background: var(--color-warning);
    color: var(--color-primary-white);
}

.badge-error {
    background: var(--color-error);
    color: var(--color-primary-white);
}

.badge-outline {
    background: transparent;
    border: 1px solid currentColor;
}

/* ========================================
   ALERT COMPONENTS
   ======================================== */

.alert {
    padding: var(--spacing-4);
    border-radius: var(--border-radius-md);
    border: 1px solid transparent;
    margin-bottom: var(--spacing-4);
}

.alert-info {
    background: rgba(59, 130, 246, 0.1);
    border-color: var(--color-info);
    color: var(--color-info);
}

.alert-success {
    background: rgba(34, 197, 94, 0.1);
    border-color: var(--color-success);
    color: var(--color-success);
}

.alert-warning {
    background: rgba(251, 146, 60, 0.1);
    border-color: var(--color-warning);
    color: var(--color-warning);
}

.alert-error {
    background: rgba(239, 68, 68, 0.1);
    border-color: var(--color-error);
    color: var(--color-error);
}

/* ========================================
   LOADING COMPONENTS
   ======================================== */

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--color-secondary-very-light-gray-blue);
    border-top-color: var(--color-primary-teal);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.spinner-sm {
    width: 16px;
    height: 16px;
    border-width: 1px;
}

.spinner-lg {
    width: 32px;
    height: 32px;
    border-width: 3px;
}

/* ========================================
   UTILITY COMPONENTS
   ======================================== */

.divider {
    height: 1px;
    background: var(--color-secondary-very-light-gray-blue);
    margin: var(--spacing-4) 0;
}

.divider-vertical {
    width: 1px;
    height: 100%;
    background: var(--color-secondary-very-light-gray-blue);
    margin: 0 var(--spacing-4);
}

.skeleton {
    background: linear-gradient(90deg, var(--color-tertiary-light-gray) 25%, var(--color-secondary-very-light-gray-blue) 50%, var(--color-tertiary-light-gray) 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: var(--border-radius-sm);
}

@keyframes skeleton-loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* ========================================
   NAVIGATION COMPONENTS
   ======================================== */

.nav {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    position: relative;
}

.nav-link {
    display: block;
    padding: var(--spacing-3) var(--spacing-4);
    color: var(--color-tertiary-dark-navy);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-base);
    border-radius: var(--border-radius-md);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--color-primary-teal);
    transition: var(--transition-normal);
    opacity: 0.1;
}

.nav-link:hover::before,
.nav-link.active::before {
    left: 0;
}

.nav-link:hover,
.nav-link.active {
    background-color: var(--color-primary-teal);
    color: var(--color-primary-white);
    transform: translateY(-1px);
}

/* ========================================
   DROPDOWN COMPONENTS
   ======================================== */

.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-toggle {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-2) var(--spacing-3);
    border-radius: var(--border-radius-md);
    transition: var(--transition-normal);
}

.dropdown-toggle:hover {
    background: var(--color-tertiary-light-gray);
}

.dropdown-menu {
    position: absolute;
    top: calc(100% + var(--spacing-1));
    left: 0;
    background: var(--color-primary-white);
    border: 1px solid var(--color-secondary-very-light-gray-blue);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-lg);
    min-width: 200px;
    z-index: var(--z-index-dropdown);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition-normal);
}

.dropdown.active .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: block;
    padding: var(--spacing-3) var(--spacing-4);
    color: var(--color-tertiary-dark-navy);
    text-decoration: none;
    font-size: var(--font-size-base);
    transition: var(--transition-fast);
    border-left: 3px solid transparent;
}

.dropdown-item:hover {
    background: var(--color-tertiary-light-gray);
    border-left-color: var(--color-primary-teal);
    color: var(--color-primary-teal);
}

.dropdown-divider {
    height: 1px;
    background: var(--color-secondary-very-light-gray-blue);
    margin: var(--spacing-2) 0;
}

/* ========================================
   SEARCH COMPONENTS
   ======================================== */

.search-container {
    position: relative;
    width: 100%;
    max-width: 400px;
}

.search-input {
    width: 100%;
    padding: var(--spacing-3) var(--spacing-10) var(--spacing-3) var(--spacing-4);
    border: 2px solid var(--color-secondary-very-light-gray-blue);
    border-radius: var(--border-radius-lg);
    background: var(--color-primary-white);
    font-size: var(--font-size-base);
    transition: var(--transition-normal);
}

.search-input:focus {
    outline: none;
    border-color: var(--color-primary-teal);
    box-shadow: 0 0 0 4px rgba(46, 192, 203, 0.1);
}

.search-icon {
    position: absolute;
    right: var(--spacing-3);
    top: 50%;
    transform: translateY(-50%);
    color: var(--color-secondary-dark-cyan);
    pointer-events: none;
}

.search-results {
    position: absolute;
    top: calc(100% + var(--spacing-2));
    left: 0;
    right: 0;
    background: var(--color-primary-white);
    border: 1px solid var(--color-secondary-very-light-gray-blue);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-lg);
    max-height: 300px;
    overflow-y: auto;
    z-index: var(--z-index-dropdown);
}

.search-result-item {
    padding: var(--spacing-3) var(--spacing-4);
    border-bottom: 1px solid var(--color-secondary-very-light-gray-blue);
    cursor: pointer;
    transition: var(--transition-fast);
}

.search-result-item:hover {
    background: var(--color-tertiary-light-gray);
}

.search-result-item:last-child {
    border-bottom: none;
}

/* ========================================
   TAB COMPONENTS
   ======================================== */

.tabs {
    width: 100%;
}

.tab-list {
    display: flex;
    border-bottom: 1px solid var(--color-secondary-very-light-gray-blue);
    background: var(--color-tertiary-light-gray);
    border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
}

.tab-button {
    background: none;
    border: none;
    padding: var(--spacing-4) var(--spacing-6);
    font-family: var(--font-family-primary);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    color: var(--color-secondary-dark-cyan);
    cursor: pointer;
    transition: var(--transition-normal);
    border-bottom: 3px solid transparent;
    position: relative;
}

.tab-button:hover {
    background-color: var(--color-secondary-very-light-blue);
    color: var(--color-tertiary-dark-navy);
}

.tab-button.active {
    background-color: var(--color-primary-white);
    color: var(--color-primary-teal);
    border-bottom-color: var(--color-primary-teal);
}

.tab-content {
    display: none;
    padding: var(--spacing-6);
    background: var(--color-primary-white);
    border-radius: 0 0 var(--border-radius-md) var(--border-radius-md);
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* ========================================
   MODAL COMPONENTS
   ======================================== */

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: var(--z-index-modal-backdrop);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-normal);
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.9);
    background: var(--color-primary-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-2xl);
    z-index: var(--z-index-modal);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    transition: var(--transition-normal);
}

.modal-overlay.active .modal {
    transform: translate(-50%, -50%) scale(1);
}

.modal-header {
    padding: var(--spacing-6);
    border-bottom: 1px solid var(--color-secondary-very-light-gray-blue);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--color-tertiary-dark-navy);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-size-xl);
    color: var(--color-secondary-dark-cyan);
    cursor: pointer;
    padding: var(--spacing-2);
    border-radius: var(--border-radius-sm);
    transition: var(--transition-fast);
}

.modal-close:hover {
    background: var(--color-tertiary-light-gray);
    color: var(--color-tertiary-dark-navy);
}

.modal-body {
    padding: var(--spacing-6);
    overflow-y: auto;
}

.modal-footer {
    padding: var(--spacing-6);
    border-top: 1px solid var(--color-secondary-very-light-gray-blue);
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-3);
}

/* ========================================
   TOOLTIP COMPONENTS
   ======================================== */

.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip-content {
    position: absolute;
    bottom: calc(100% + var(--spacing-2));
    left: 50%;
    transform: translateX(-50%);
    background: var(--color-tertiary-dark-navy);
    color: var(--color-primary-white);
    padding: var(--spacing-2) var(--spacing-3);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    white-space: nowrap;
    z-index: var(--z-index-tooltip);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-fast);
}

.tooltip:hover .tooltip-content {
    opacity: 1;
    visibility: visible;
}

.tooltip-content::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: var(--color-tertiary-dark-navy);
}
