<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>REMAN ERP Design System - Example Usage</title>
    
    <!-- Import Design System Files -->
    <link rel="stylesheet" href="design-tokens.css">
    <link rel="stylesheet" href="typography.css">
    <link rel="stylesheet" href="layout.css">
    <link rel="stylesheet" href="components.css">
    <link rel="stylesheet" href="utilities.css">
    
    <style>
        /* Custom styles for this example page */
        .demo-section {
            margin-bottom: var(--spacing-12);
            padding: var(--spacing-8);
            background: var(--color-primary-white);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-sm);
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-6);
            margin-top: var(--spacing-6);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-4);
            margin: var(--spacing-6) 0;
        }
        
        .stat-card {
            background: var(--gradient-primary);
            color: var(--color-primary-white);
            padding: var(--spacing-6);
            border-radius: var(--border-radius-lg);
            text-align: center;
            transition: var(--transition-normal);
        }
        
        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-primary-lg);
        }
        
        .stat-number {
            font-size: var(--font-size-3xl);
            font-weight: var(--font-weight-bold);
            margin-bottom: var(--spacing-2);
        }
        
        .feature-card {
            padding: var(--spacing-6);
            border: 1px solid var(--color-secondary-very-light-gray-blue);
            border-radius: var(--border-radius-lg);
            transition: var(--transition-normal);
        }
        
        .feature-card:hover {
            border-color: var(--color-primary-teal);
            box-shadow: var(--shadow-primary);
            transform: translateY(-2px);
        }
        
        .feature-icon {
            width: 48px;
            height: 48px;
            background: var(--gradient-primary);
            border-radius: var(--border-radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: var(--spacing-4);
            color: var(--color-primary-white);
        }
    </style>
</head>
<body class="bg-light">
    <!-- Header -->
    <header class="bg-tertiary py-4">
        <div class="container container-xl">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="heading-2 text-white m-0">REMAN ERP</h1>
                    <p class="text-caption text-white opacity-75 m-0">Enterprise Resource Planning</p>
                </div>
                <nav class="flex gap-4">
                    <a href="#" class="btn btn-ghost text-white">Dashboard</a>
                    <a href="#" class="btn btn-ghost text-white">Reports</a>
                    <a href="#" class="btn btn-primary">Get Started</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container container-xl py-8">
        <!-- Hero Section -->
        <section class="demo-section text-center">
            <h1 class="heading-1 text-gradient-primary">Welcome to REMAN ERP</h1>
            <p class="text-body-large text-secondary mx-auto" style="max-width: 600px;">
                Experience the power of our comprehensive design system that ensures consistency, 
                scalability, and maintainability across all your applications.
            </p>
            
            <!-- Stats Grid -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">1,247</div>
                    <div class="text-overline">Total Parts</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">324</div>
                    <div class="text-overline">Active Customers</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">89</div>
                    <div class="text-overline">Reports Generated</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">156</div>
                    <div class="text-overline">Suppliers</div>
                </div>
            </div>
            
            <div class="flex justify-center gap-4 mt-8">
                <button class="btn btn-primary btn-lg">Explore Features</button>
                <button class="btn btn-outline btn-lg">View Documentation</button>
            </div>
        </section>

        <!-- Features Section -->
        <section class="demo-section">
            <h2 class="heading-2 text-center mb-6">Key Features</h2>
            <div class="demo-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 2L2 7l10 5 10-5-10-5z" stroke="currentColor" stroke-width="2"/>
                            <path d="M2 17l10 5 10-5" stroke="currentColor" stroke-width="2"/>
                            <path d="M2 12l10 5 10-5" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <h3 class="heading-4">Parts Management</h3>
                    <p class="text-body-small">Comprehensive parts inventory with real-time tracking and automated reordering.</p>
                    <div class="mt-4">
                        <span class="badge badge-primary">New</span>
                        <span class="badge badge-success">Active</span>
                    </div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2"/>
                            <circle cx="8.5" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                            <path d="M20 8v6M23 11h-6" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <h3 class="heading-4">Customer Relations</h3>
                    <p class="text-body-small">Manage customer relationships with integrated communication tools and history tracking.</p>
                    <div class="mt-4">
                        <span class="badge badge-info">Updated</span>
                    </div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
                            <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
                            <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2"/>
                            <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <h3 class="heading-4">Advanced Reporting</h3>
                    <p class="text-body-small">Generate detailed reports with customizable templates and automated scheduling.</p>
                    <div class="mt-4">
                        <span class="badge badge-warning">Beta</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Form Example Section -->
        <section class="demo-section">
            <h2 class="heading-2 mb-6">Form Components</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                    <h3 class="heading-4 mb-4">Customer Information</h3>
                    <form>
                        <div class="form-group">
                            <label class="form-label required">Company Name</label>
                            <input type="text" class="form-input" placeholder="Enter company name">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label required">Email Address</label>
                            <input type="email" class="form-input" placeholder="<EMAIL>">
                            <div class="form-help">We'll use this for important notifications</div>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Industry</label>
                            <select class="form-select">
                                <option>Select industry</option>
                                <option>Manufacturing</option>
                                <option>Automotive</option>
                                <option>Technology</option>
                                <option>Healthcare</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Additional Notes</label>
                            <textarea class="form-textarea" placeholder="Any additional information..."></textarea>
                        </div>
                        
                        <div class="flex gap-3">
                            <button type="submit" class="btn btn-primary">Save Customer</button>
                            <button type="button" class="btn btn-outline">Cancel</button>
                        </div>
                    </form>
                </div>
                
                <div>
                    <h3 class="heading-4 mb-4">Quick Actions</h3>
                    <div class="grid gap-4">
                        <div class="card">
                            <div class="card-body">
                                <h4 class="card-title">Generate Report</h4>
                                <p class="text-body-small">Create a new report with current data</p>
                                <button class="btn btn-primary btn-sm mt-3">Generate</button>
                            </div>
                        </div>
                        
                        <div class="card">
                            <div class="card-body">
                                <h4 class="card-title">Import Data</h4>
                                <p class="text-body-small">Upload CSV or Excel files</p>
                                <button class="btn btn-secondary btn-sm mt-3">Upload</button>
                            </div>
                        </div>
                        
                        <div class="card">
                            <div class="card-body">
                                <h4 class="card-title">System Backup</h4>
                                <p class="text-body-small">Create a backup of your data</p>
                                <button class="btn btn-outline btn-sm mt-3">Backup</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Alerts Section -->
        <section class="demo-section">
            <h2 class="heading-2 mb-6">System Notifications</h2>
            <div class="grid gap-4">
                <div class="alert alert-info">
                    <strong>Info:</strong> System maintenance is scheduled for tonight at 2:00 AM EST.
                </div>
                <div class="alert alert-success">
                    <strong>Success:</strong> Your data has been successfully backed up.
                </div>
                <div class="alert alert-warning">
                    <strong>Warning:</strong> Low inventory detected for 5 items. Consider reordering.
                </div>
                <div class="alert alert-error">
                    <strong>Error:</strong> Failed to connect to external API. Please check your connection.
                </div>
            </div>
        </section>

        <!-- Tabs Example -->
        <section class="demo-section">
            <h2 class="heading-2 mb-6">Tabbed Interface</h2>
            <div class="tabs">
                <div class="tab-list">
                    <button class="tab-button active" onclick="showTab('overview')">Overview</button>
                    <button class="tab-button" onclick="showTab('details')">Details</button>
                    <button class="tab-button" onclick="showTab('settings')">Settings</button>
                </div>
                <div id="overview" class="tab-content active">
                    <h3 class="heading-4">System Overview</h3>
                    <p class="text-body">This is the overview tab content. Here you can see a summary of your system status and key metrics.</p>
                    <div class="mt-4">
                        <span class="badge badge-success">System Online</span>
                        <span class="badge badge-primary">24/7 Support</span>
                    </div>
                </div>
                <div id="details" class="tab-content">
                    <h3 class="heading-4">Detailed Information</h3>
                    <p class="text-body">This tab contains detailed information about your system configuration and performance metrics.</p>
                </div>
                <div id="settings" class="tab-content">
                    <h3 class="heading-4">System Settings</h3>
                    <p class="text-body">Configure your system preferences and manage user permissions from this section.</p>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-tertiary py-8 mt-12">
        <div class="container container-xl">
            <div class="text-center">
                <p class="text-white opacity-75">© 2024 REMAN ERP. Built with the REMAN Design System.</p>
            </div>
        </div>
    </footer>

    <script>
        // Simple tab functionality
        function showTab(tabId) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });
            
            // Show selected tab content
            document.getElementById(tabId).classList.add('active');
            
            // Add active class to clicked button
            event.target.classList.add('active');
        }
    </script>
</body>
</html>
