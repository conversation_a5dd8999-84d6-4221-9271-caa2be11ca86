<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>REMAN ERP Design System Guide</title>
    <link rel="stylesheet" href="design-tokens.css">
    <link rel="stylesheet" href="typography.css">
    <link rel="stylesheet" href="layout.css">
    <link rel="stylesheet" href="components.css">
    <style>
        /* Documentation specific styles */
        .doc-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--spacing-8);
        }
        
        .doc-header {
            text-align: center;
            margin-bottom: var(--spacing-12);
            padding: var(--spacing-8);
            background: var(--gradient-primary);
            color: var(--color-primary-white);
            border-radius: var(--border-radius-lg);
        }
        
        .doc-section {
            margin-bottom: var(--spacing-12);
        }
        
        .doc-section-title {
            font-size: var(--font-size-2xl);
            font-weight: var(--font-weight-bold);
            color: var(--color-tertiary-dark-navy);
            margin-bottom: var(--spacing-6);
            padding-bottom: var(--spacing-3);
            border-bottom: 2px solid var(--color-primary-teal);
        }
        
        .color-palette {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-4);
            margin-bottom: var(--spacing-6);
        }
        
        .color-item {
            text-align: center;
            border-radius: var(--border-radius-md);
            overflow: hidden;
            box-shadow: var(--shadow-md);
        }
        
        .color-swatch {
            height: 80px;
            width: 100%;
        }
        
        .color-info {
            padding: var(--spacing-3);
            background: var(--color-primary-white);
        }
        
        .color-name {
            font-weight: var(--font-weight-semibold);
            margin-bottom: var(--spacing-1);
        }
        
        .color-value {
            font-family: var(--font-family-monospace);
            font-size: var(--font-size-sm);
            color: var(--color-secondary-dark-cyan);
        }
        
        .component-showcase {
            display: grid;
            gap: var(--spacing-6);
            margin-bottom: var(--spacing-8);
        }
        
        .component-example {
            padding: var(--spacing-6);
            border: 1px solid var(--color-secondary-very-light-gray-blue);
            border-radius: var(--border-radius-md);
            background: var(--color-primary-white);
        }
        
        .component-title {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            margin-bottom: var(--spacing-4);
            color: var(--color-tertiary-dark-navy);
        }
        
        .component-demo {
            margin-bottom: var(--spacing-4);
            padding: var(--spacing-4);
            background: var(--color-tertiary-light-gray);
            border-radius: var(--border-radius-sm);
        }
        
        .code-block {
            background: var(--color-tertiary-dark-navy);
            color: var(--color-primary-white);
            padding: var(--spacing-4);
            border-radius: var(--border-radius-sm);
            font-family: var(--font-family-monospace);
            font-size: var(--font-size-sm);
            overflow-x: auto;
        }
        
        .typography-scale {
            display: grid;
            gap: var(--spacing-4);
        }
        
        .typography-item {
            padding: var(--spacing-4);
            border: 1px solid var(--color-secondary-very-light-gray-blue);
            border-radius: var(--border-radius-sm);
        }
        
        .spacing-demo {
            display: flex;
            align-items: center;
            gap: var(--spacing-4);
            margin-bottom: var(--spacing-2);
        }
        
        .spacing-box {
            background: var(--color-primary-teal);
            color: var(--color-primary-white);
            padding: var(--spacing-2);
            border-radius: var(--border-radius-sm);
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
        }
        
        .nav-demo {
            background: var(--color-secondary-very-light-gray-blue);
            padding: var(--spacing-4);
            border-radius: var(--border-radius-md);
        }
    </style>
</head>
<body>
    <div class="doc-container">
        <!-- Header -->
        <header class="doc-header">
            <h1 class="heading-1">REMAN ERP Design System</h1>
            <p class="text-body-large">A comprehensive design system for consistent, scalable, and maintainable user interfaces</p>
        </header>

        <!-- Color Palette Section -->
        <section class="doc-section">
            <h2 class="doc-section-title">Color Palette</h2>
            <p class="text-body">Our color system is built around a professional teal-based palette with supporting colors for various UI states and semantic meanings.</p>
            
            <h3 class="heading-4">Primary Colors</h3>
            <div class="color-palette">
                <div class="color-item">
                    <div class="color-swatch" style="background: var(--color-primary-teal);"></div>
                    <div class="color-info">
                        <div class="color-name">Primary Teal</div>
                        <div class="color-value">#2EC0CB</div>
                    </div>
                </div>
                <div class="color-item">
                    <div class="color-swatch" style="background: var(--color-primary-dark-teal);"></div>
                    <div class="color-info">
                        <div class="color-name">Primary Dark Teal</div>
                        <div class="color-value">#23A3AD</div>
                    </div>
                </div>
                <div class="color-item">
                    <div class="color-swatch" style="background: var(--color-primary-blue);"></div>
                    <div class="color-info">
                        <div class="color-name">Primary Blue</div>
                        <div class="color-value">#0F5FDC</div>
                    </div>
                </div>
                <div class="color-item">
                    <div class="color-swatch" style="background: var(--color-tertiary-dark-navy);"></div>
                    <div class="color-info">
                        <div class="color-name">Dark Navy</div>
                        <div class="color-value">#000032</div>
                    </div>
                </div>
            </div>

            <h3 class="heading-4">Semantic Colors</h3>
            <div class="color-palette">
                <div class="color-item">
                    <div class="color-swatch" style="background: var(--color-success);"></div>
                    <div class="color-info">
                        <div class="color-name">Success</div>
                        <div class="color-value">#22C55E</div>
                    </div>
                </div>
                <div class="color-item">
                    <div class="color-swatch" style="background: var(--color-warning);"></div>
                    <div class="color-info">
                        <div class="color-name">Warning</div>
                        <div class="color-value">#FB923C</div>
                    </div>
                </div>
                <div class="color-item">
                    <div class="color-swatch" style="background: var(--color-error);"></div>
                    <div class="color-info">
                        <div class="color-name">Error</div>
                        <div class="color-value">#EF4444</div>
                    </div>
                </div>
                <div class="color-item">
                    <div class="color-swatch" style="background: var(--color-info);"></div>
                    <div class="color-info">
                        <div class="color-name">Info</div>
                        <div class="color-value">#3B82F6</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Typography Section -->
        <section class="doc-section">
            <h2 class="doc-section-title">Typography</h2>
            <p class="text-body">Our typography system uses the HCLTech Roobert font family with a carefully crafted scale for optimal readability and hierarchy.</p>
            
            <div class="typography-scale">
                <div class="typography-item">
                    <h1 class="heading-1">Heading 1 - Main Page Titles</h1>
                    <code class="text-sm">font-size: 2rem (32px), font-weight: 700</code>
                </div>
                <div class="typography-item">
                    <h2 class="heading-2">Heading 2 - Section Titles</h2>
                    <code class="text-sm">font-size: 1.75rem (28px), font-weight: 600</code>
                </div>
                <div class="typography-item">
                    <h3 class="heading-3">Heading 3 - Subsection Titles</h3>
                    <code class="text-sm">font-size: 1.5rem (24px), font-weight: 600</code>
                </div>
                <div class="typography-item">
                    <p class="text-body">Body Text - Regular paragraph text for content</p>
                    <code class="text-sm">font-size: 1rem (16px), font-weight: 400</code>
                </div>
                <div class="typography-item">
                    <p class="text-caption">Caption Text - Secondary information and labels</p>
                    <code class="text-sm">font-size: 0.8rem (13px), font-weight: 400</code>
                </div>
            </div>
        </section>

        <!-- Spacing Section -->
        <section class="doc-section">
            <h2 class="doc-section-title">Spacing System</h2>
            <p class="text-body">Our spacing system is based on a 4px base unit, providing consistent rhythm and alignment throughout the interface.</p>
            
            <div class="spacing-demo">
                <div class="spacing-box" style="padding: var(--spacing-1);">XS (4px)</div>
                <div class="spacing-box" style="padding: var(--spacing-2);">SM (8px)</div>
                <div class="spacing-box" style="padding: var(--spacing-4);">MD (16px)</div>
                <div class="spacing-box" style="padding: var(--spacing-6);">LG (24px)</div>
                <div class="spacing-box" style="padding: var(--spacing-8);">XL (32px)</div>
                <div class="spacing-box" style="padding: var(--spacing-12);">XXL (48px)</div>
            </div>
        </section>

        <!-- Component Examples -->
        <section class="doc-section">
            <h2 class="doc-section-title">Components</h2>
            
            <div class="component-showcase">
                <!-- Buttons -->
                <div class="component-example">
                    <h3 class="component-title">Buttons</h3>
                    <div class="component-demo">
                        <button class="btn btn-primary">Primary Button</button>
                        <button class="btn btn-secondary">Secondary Button</button>
                        <button class="btn btn-outline">Outline Button</button>
                        <button class="btn btn-ghost">Ghost Button</button>
                    </div>
                    <div class="code-block">
&lt;button class="btn btn-primary"&gt;Primary Button&lt;/button&gt;
&lt;button class="btn btn-secondary"&gt;Secondary Button&lt;/button&gt;
&lt;button class="btn btn-outline"&gt;Outline Button&lt;/button&gt;
&lt;button class="btn btn-ghost"&gt;Ghost Button&lt;/button&gt;
                    </div>
                </div>

                <!-- Form Elements -->
                <div class="component-example">
                    <h3 class="component-title">Form Elements</h3>
                    <div class="component-demo">
                        <div class="form-group">
                            <label class="form-label required">Email Address</label>
                            <input type="email" class="form-input" placeholder="Enter your email">
                            <div class="form-help">We'll never share your email with anyone else.</div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Message</label>
                            <textarea class="form-textarea" placeholder="Enter your message"></textarea>
                        </div>
                    </div>
                </div>

                <!-- Cards -->
                <div class="component-example">
                    <h3 class="component-title">Cards</h3>
                    <div class="component-demo">
                        <div class="card" style="max-width: 300px;">
                            <div class="card-header">
                                <h4 class="card-title">Card Title</h4>
                                <p class="card-subtitle">Card subtitle</p>
                            </div>
                            <div class="card-body">
                                <p>This is the card content area where you can place any information.</p>
                            </div>
                            <div class="card-footer">
                                <button class="btn btn-primary btn-sm">Action</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Badges -->
                <div class="component-example">
                    <h3 class="component-title">Badges</h3>
                    <div class="component-demo">
                        <span class="badge badge-primary">Primary</span>
                        <span class="badge badge-success">Success</span>
                        <span class="badge badge-warning">Warning</span>
                        <span class="badge badge-error">Error</span>
                        <span class="badge badge-secondary">Secondary</span>
                    </div>
                </div>

                <!-- Alerts -->
                <div class="component-example">
                    <h3 class="component-title">Alerts</h3>
                    <div class="component-demo">
                        <div class="alert alert-info">This is an info alert message.</div>
                        <div class="alert alert-success">This is a success alert message.</div>
                        <div class="alert alert-warning">This is a warning alert message.</div>
                        <div class="alert alert-error">This is an error alert message.</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Usage Guidelines -->
        <section class="doc-section">
            <h2 class="doc-section-title">Usage Guidelines</h2>
            <div class="card">
                <div class="card-body">
                    <h3 class="heading-4">Getting Started</h3>
                    <p class="text-body">To use the REMAN ERP Design System in your project:</p>
                    <ol>
                        <li>Import the design tokens first: <code>@import url('./design-tokens.css');</code></li>
                        <li>Import typography: <code>@import url('./typography.css');</code></li>
                        <li>Import layout utilities: <code>@import url('./layout.css');</code></li>
                        <li>Import components: <code>@import url('./components.css');</code></li>
                    </ol>
                    
                    <h3 class="heading-4">Best Practices</h3>
                    <ul>
                        <li>Always use design tokens instead of hardcoded values</li>
                        <li>Follow the spacing system for consistent layouts</li>
                        <li>Use semantic color names for better maintainability</li>
                        <li>Test components across different screen sizes</li>
                        <li>Maintain accessibility standards with proper contrast ratios</li>
                    </ul>
                </div>
            </div>
        </section>
    </div>
</body>
</html>
