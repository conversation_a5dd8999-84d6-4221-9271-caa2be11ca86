/**
 * REMAN ERP Design System - Typography
 * 
 * This file contains all typography-related styles including font definitions,
 * text utilities, and typographic scales. Import design-tokens.css first.
 * 
 * Usage: @import url('./typography.css');
 */

/* ========================================
   FONT FACE DECLARATIONS
   ======================================== */

@font-face {
    font-family: 'HCLTech Roobert';
    src: url('HCLTech Roobert_font/HCLTech Roobert_font/WOFF2/HCLTechRoobert-Light.woff2') format('woff2');
    font-weight: var(--font-weight-light);
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'HCLTech Roobert';
    src: url('HCLTech Roobert_font/HCLTech Roobert_font/WOFF2/HCLTechRoobert-Regular.woff2') format('woff2');
    font-weight: var(--font-weight-regular);
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'HCLTech Roobert';
    src: url('HCLTech Roobert_font/HCLTech Roobert_font/WOFF2/HCLTechRoobert-Medium.woff2') format('woff2');
    font-weight: var(--font-weight-medium);
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'HCLTech Roobert';
    src: url('HCLTech Roobert_font/HCLTech Roobert_font/WOFF2/HCLTechRoobert-Bold.woff2') format('woff2');
    font-weight: var(--font-weight-bold);
    font-style: normal;
    font-display: swap;
}

/* ========================================
   BASE TYPOGRAPHY
   ======================================== */

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-regular);
    font-size: var(--font-size-base);
    line-height: var(--line-height-relaxed);
    color: var(--color-tertiary-black);
    letter-spacing: var(--letter-spacing-normal);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ========================================
   HEADING STYLES
   ======================================== */

.heading-1,
.h1 {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
    letter-spacing: var(--letter-spacing-tight);
    color: var(--color-tertiary-dark-navy);
    margin: 0 0 var(--spacing-lg) 0;
}

.heading-2,
.h2 {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-tight);
    letter-spacing: var(--letter-spacing-tight);
    color: var(--color-tertiary-dark-navy);
    margin: 0 0 var(--spacing-md) 0;
}

.heading-3,
.h3 {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-normal);
    color: var(--color-tertiary-dark-navy);
    margin: 0 0 var(--spacing-md) 0;
}

.heading-4,
.h4 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-normal);
    color: var(--color-tertiary-dark-navy);
    margin: 0 0 var(--spacing-sm) 0;
}

.heading-5,
.h5 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-normal);
    color: var(--color-tertiary-dark-navy);
    margin: 0 0 var(--spacing-sm) 0;
}

.heading-6,
.h6 {
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-normal);
    color: var(--color-secondary-dark-cyan);
    margin: 0 0 var(--spacing-sm) 0;
    text-transform: uppercase;
    letter-spacing: var(--letter-spacing-wide);
}

/* ========================================
   BODY TEXT STYLES
   ======================================== */

.text-body-large {
    font-size: var(--font-size-lg);
    line-height: var(--line-height-relaxed);
    color: var(--color-tertiary-black);
}

.text-body {
    font-size: var(--font-size-md);
    line-height: var(--line-height-relaxed);
    color: var(--color-tertiary-black);
}

.text-body-small {
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
    color: var(--color-secondary-dark-cyan);
}

.text-caption {
    font-size: var(--font-size-sm);
    line-height: var(--line-height-normal);
    color: var(--color-secondary-dark-cyan);
}

.text-overline {
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-normal);
    color: var(--color-secondary-dark-cyan);
    text-transform: uppercase;
    letter-spacing: var(--letter-spacing-wider);
}

/* ========================================
   UTILITY CLASSES
   ======================================== */

/* Font Weights */
.font-light { font-weight: var(--font-weight-light); }
.font-regular { font-weight: var(--font-weight-regular); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }
.font-extrabold { font-weight: var(--font-weight-extrabold); }

/* Font Sizes */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-md { font-size: var(--font-size-md); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }
.text-4xl { font-size: var(--font-size-4xl); }
.text-5xl { font-size: var(--font-size-5xl); }

/* Text Colors */
.text-primary { color: var(--color-primary-teal); }
.text-primary-dark { color: var(--color-primary-dark-teal); }
.text-secondary { color: var(--color-secondary-dark-cyan); }
.text-tertiary { color: var(--color-tertiary-dark-navy); }
.text-success { color: var(--color-success); }
.text-warning { color: var(--color-warning); }
.text-error { color: var(--color-error); }
.text-info { color: var(--color-info); }
.text-white { color: var(--color-primary-white); }
.text-muted { color: var(--color-secondary-dark-cyan); opacity: 0.7; }

/* Text Alignment */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

/* Text Transform */
.text-uppercase { text-transform: uppercase; }
.text-lowercase { text-transform: lowercase; }
.text-capitalize { text-transform: capitalize; }
.text-normal-case { text-transform: none; }

/* Text Decoration */
.text-underline { text-decoration: underline; }
.text-line-through { text-decoration: line-through; }
.text-no-underline { text-decoration: none; }

/* Line Heights */
.leading-tight { line-height: var(--line-height-tight); }
.leading-normal { line-height: var(--line-height-normal); }
.leading-relaxed { line-height: var(--line-height-relaxed); }
.leading-loose { line-height: var(--line-height-loose); }

/* Letter Spacing */
.tracking-tight { letter-spacing: var(--letter-spacing-tight); }
.tracking-normal { letter-spacing: var(--letter-spacing-normal); }
.tracking-wide { letter-spacing: var(--letter-spacing-wide); }
.tracking-wider { letter-spacing: var(--letter-spacing-wider); }

/* ========================================
   SPECIAL TEXT STYLES
   ======================================== */

.text-gradient-primary {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: var(--font-weight-bold);
}

.text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* ========================================
   MONOSPACE TEXT
   ======================================== */

.font-mono,
code,
kbd,
pre {
    font-family: var(--font-family-monospace);
}

code {
    background: var(--color-tertiary-light-gray);
    padding: var(--spacing-0-5) var(--spacing-1);
    border-radius: var(--border-radius-sm);
    font-size: 0.875em;
    color: var(--color-tertiary-dark-navy);
}

kbd {
    background: var(--color-primary-white);
    border: 1px solid var(--color-secondary-very-light-gray-blue);
    border-radius: var(--border-radius-xs);
    padding: var(--spacing-0-5) var(--spacing-1);
    font-size: var(--font-size-xs);
    font-family: var(--font-family-monospace);
    color: var(--color-tertiary-dark-navy);
    box-shadow: var(--shadow-xs);
}

/* ========================================
   RESPONSIVE TYPOGRAPHY
   ======================================== */

@media (max-width: 768px) {
    .heading-1, .h1 { font-size: var(--font-size-3xl); }
    .heading-2, .h2 { font-size: var(--font-size-2xl); }
    .heading-3, .h3 { font-size: var(--font-size-xl); }
    .heading-4, .h4 { font-size: var(--font-size-lg); }
    .heading-5, .h5 { font-size: var(--font-size-md); }
    
    .text-body-large { font-size: var(--font-size-md); }
    .text-body { font-size: var(--font-size-base); }
}

@media (max-width: 480px) {
    .heading-1, .h1 { font-size: var(--font-size-2xl); }
    .heading-2, .h2 { font-size: var(--font-size-xl); }
    .heading-3, .h3 { font-size: var(--font-size-lg); }
}
